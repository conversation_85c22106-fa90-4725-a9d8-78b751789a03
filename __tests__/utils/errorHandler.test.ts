// Mock React Native dependencies
jest.mock('@react-native-async-storage/async-storage', () => ({
  getItem: jest.fn(),
  setItem: jest.fn(),
}));

jest.mock('expo-constants', () => ({
  expoConfig: {
    extra: {
      apiUrl: 'http://test-api.com',
    },
  },
}));

import { handleError, ErrorType, ErrorSeverity, getErrorActionMessage, logError } from '../../utils/errorHandler';
import { ApiError } from '../../services/api';

// Mock the localization
jest.mock('../../constants/Localization', () => ({
  t: (key: string) => {
    const translations: Record<string, string> = {
      badRequest: 'Bad request',
      unauthorized: 'Unauthorized - please login',
      forbidden: 'Access forbidden',
      notFound: 'Data not found',
      conflict: 'Data conflict',
      internalServerError: 'Internal server error',
      serviceUnavailable: 'Service unavailable',
      validationError: 'Validation error',
      serverError: 'Server error',
      connectionError: 'Internet connection error',
      timeoutError: 'Connection timeout',
      unknownError: 'An unknown error occurred',
      checkConnection: 'Please check your internet connection',
      retry: 'Retry',
      tryAgainLater: 'Please try again later',
    };
    return translations[key] || key;
  },
}));

describe('Error Handler', () => {
  beforeEach(() => {
    // Clear console mocks
    jest.clearAllMocks();
    // Mock console methods
    global.console = {
      ...console,
      error: jest.fn(),
      group: jest.fn(),
      groupEnd: jest.fn(),
    };
  });

  describe('handleError', () => {
    it('should handle ApiError correctly', () => {
      const apiError = new ApiError('Test error', 400, { field: 'invalid' });
      const result = handleError(apiError);

      expect(result.type).toBe(ErrorType.VALIDATION);
      expect(result.severity).toBe(ErrorSeverity.MEDIUM);
      expect(result.statusCode).toBe(400);
      expect(result.canRetry).toBe(false);
      expect(result.userMessage).toBe('Bad request');
    });

    it('should handle server errors (5xx) correctly', () => {
      const serverError = new ApiError('Server error', 500);
      const result = handleError(serverError);

      expect(result.type).toBe(ErrorType.SERVER);
      expect(result.severity).toBe(ErrorSeverity.CRITICAL);
      expect(result.canRetry).toBe(true);
      expect(result.userMessage).toBe('Internal server error');
    });

    it('should handle network errors correctly', () => {
      const networkError = new Error('Network request failed!');
      const result = handleError(networkError);

      expect(result.type).toBe(ErrorType.NETWORK);
      expect(result.severity).toBe(ErrorSeverity.MEDIUM);
      expect(result.canRetry).toBe(true);
      expect(result.userMessage).toBe('Internet connection error');
    });

    it('should handle timeout errors correctly', () => {
      const timeoutError = new Error('Connection timeout!');
      const result = handleError(timeoutError);

      expect(result.type).toBe(ErrorType.TIMEOUT);
      expect(result.severity).toBe(ErrorSeverity.LOW);
      expect(result.canRetry).toBe(true);
      expect(result.userMessage).toBe('Connection timeout');
    });

    it('should handle generic errors correctly', () => {
      const genericError = new Error('Something went wrong!');
      const result = handleError(genericError);

      expect(result.type).toBe(ErrorType.UNKNOWN);
      expect(result.severity).toBe(ErrorSeverity.LOW);
      expect(result.message).toBe('Something went wrong!');
      expect(result.userMessage).toBe('An unknown error occurred');
    });

    it('should handle string errors correctly', () => {
      const stringError = 'String error message';
      const result = handleError(stringError);

      expect(result.type).toBe(ErrorType.UNKNOWN);
      expect(result.message).toBe('String error message');
      expect(result.userMessage).toBe('An unknown error occurred');
    });

    it('should handle unauthorized errors correctly', () => {
      const unauthorizedError = new ApiError('Unauthorized', 401);
      const result = handleError(unauthorizedError);

      expect(result.type).toBe(ErrorType.VALIDATION);
      expect(result.severity).toBe(ErrorSeverity.HIGH);
      expect(result.canRetry).toBe(false);
      expect(result.userMessage).toBe('Unauthorized - please login');
    });
  });

  describe('getErrorActionMessage', () => {
    it('should return check connection message for network errors', () => {
      const networkErrorInfo = {
        type: ErrorType.NETWORK,
        severity: ErrorSeverity.MEDIUM,
        message: 'Network error',
        userMessage: 'Connection error',
        canRetry: true,
      };

      const actionMessage = getErrorActionMessage(networkErrorInfo);
      expect(actionMessage).toBe('Please check your internet connection');
    });

    it('should return retry message for retryable errors', () => {
      const retryableErrorInfo = {
        type: ErrorType.SERVER,
        severity: ErrorSeverity.HIGH,
        message: 'Server error',
        userMessage: 'Server error',
        canRetry: true,
      };

      const actionMessage = getErrorActionMessage(retryableErrorInfo);
      expect(actionMessage).toBe('Retry');
    });

    it('should return try again later message for non-retryable errors', () => {
      const nonRetryableErrorInfo = {
        type: ErrorType.VALIDATION,
        severity: ErrorSeverity.MEDIUM,
        message: 'Validation error',
        userMessage: 'Validation error',
        canRetry: false,
      };

      const actionMessage = getErrorActionMessage(nonRetryableErrorInfo);
      expect(actionMessage).toBe('Please try again later');
    });
  });

  describe('logError', () => {
    it('should log error in development mode', () => {
      // Mock __DEV__ to be true
      (global as any).__DEV__ = true;

      const errorInfo = {
        type: ErrorType.NETWORK,
        severity: ErrorSeverity.MEDIUM,
        message: 'Network error',
        userMessage: 'Connection error',
        canRetry: true,
        statusCode: undefined,
        originalError: new Error('Network error'),
      };

      logError(errorInfo, 'Test Context');

      expect(console.group).toHaveBeenCalledWith('🚨 Error in Test Context');
      expect(console.error).toHaveBeenCalledWith('Type:', ErrorType.NETWORK);
      expect(console.error).toHaveBeenCalledWith('Severity:', ErrorSeverity.MEDIUM);
      expect(console.groupEnd).toHaveBeenCalled();
    });

    it('should not log error in production mode', () => {
      // Mock __DEV__ to be false
      (global as any).__DEV__ = false;

      const errorInfo = {
        type: ErrorType.NETWORK,
        severity: ErrorSeverity.MEDIUM,
        message: 'Network error',
        userMessage: 'Connection error',
        canRetry: true,
      };

      logError(errorInfo, 'Test Context');

      expect(console.group).not.toHaveBeenCalled();
      expect(console.error).not.toHaveBeenCalled();
      expect(console.groupEnd).not.toHaveBeenCalled();
    });
  });

  describe('Error severity classification', () => {
    it('should classify 500 errors as critical', () => {
      const error = new ApiError('Internal server error', 500);
      const result = handleError(error);
      expect(result.severity).toBe(ErrorSeverity.CRITICAL);
    });

    it('should classify 401/403 errors as high severity', () => {
      const unauthorizedError = new ApiError('Unauthorized', 401);
      const forbiddenError = new ApiError('Forbidden', 403);
      
      expect(handleError(unauthorizedError).severity).toBe(ErrorSeverity.HIGH);
      expect(handleError(forbiddenError).severity).toBe(ErrorSeverity.HIGH);
    });

    it('should classify 4xx errors as medium severity', () => {
      const badRequestError = new ApiError('Bad request', 400);
      const notFoundError = new ApiError('Not found', 404);
      
      expect(handleError(badRequestError).severity).toBe(ErrorSeverity.MEDIUM);
      expect(handleError(notFoundError).severity).toBe(ErrorSeverity.MEDIUM);
    });
  });

  describe('Retry capability determination', () => {
    it('should allow retry for network errors', () => {
      const networkError = new Error('fetch failed!');
      const result = handleError(networkError);
      expect(result.canRetry).toBe(true);
    });

    it('should allow retry for server errors (5xx)', () => {
      const serverError = new ApiError('Internal server error', 500);
      const result = handleError(serverError);
      expect(result.canRetry).toBe(true);
    });

    it('should not allow retry for client errors (4xx)', () => {
      const clientError = new ApiError('Bad request', 400);
      const result = handleError(clientError);
      expect(result.canRetry).toBe(false);
    });

    it('should allow retry for rate limiting (429)', () => {
      const rateLimitError = new ApiError('Too many requests', 429);
      const result = handleError(rateLimitError);
      expect(result.canRetry).toBe(true);
    });
  });
});
