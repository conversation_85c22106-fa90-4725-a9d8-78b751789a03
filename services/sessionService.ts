import { api } from './api';

import { User } from './userService';
import { Exam } from './examService';
import { Answer } from './answerService';

// Session result types
export interface CourseResult {
  courseId: number;
  courseName: string;
  totalTests: number;
  correctAnswers: number;
  wrongAnswers: number;
  unanswered: number;
  courseScore: number;
}

export interface CourseGroupResult {
  courseGroupId: number;
  courseGroupName: string;
  averageScore: number;
  weightedAverageScore: number;
}

// Define types
export interface Session {
  id: string;
  user: User;
  exam: Exam;
  start_time: string;
  end_time: string | null;
  status: string;
  score?: number;
  answers: Answer[];
  courseResults?: CourseResult[];
  courseGroupResults?: CourseGroupResult[];
  overallSessionScore?: number;
  created_at?: string;
  updated_at?: string;
}

export interface CreateSessionDto {
  userId: number;
  examId: number;
  start_time: string;
  end_time: string;
  status: string;
  score: number;
}

export interface UpdateSessionDto {
  userId?: number;
  examId?: number;
  start_time?: string;
  end_time?: string;
  status?: string;
  score?: number;
}



// Session service for handling session-related API calls
export const sessionService = {
  // Get all sessions
  async getSessions(): Promise<Session[]> {
    const response = await api.get<Session[]>('/v1/api/sessions');
    return response.data;
  },

  // Get sessions for a specific user
  async getUserSessions(userId: string): Promise<Session[]> {
    const response = await api.get<Session[]>(`/v1/api/sessions/user/${userId}`);
    return response.data;
  },

  // Get a specific session
  async getSession(id: string): Promise<Session> {
    const response = await api.get<Session>(`/v1/api/sessions/${id}`);
    return response.data;
  },

  // Create a new session
  async createSession(sessionData: CreateSessionDto): Promise<Session> {
    const response = await api.post<Session>('/v1/api/sessions', sessionData);
    return response.data;
  },

  // Update a session
  async updateSession(id: string, sessionData: UpdateSessionDto): Promise<Session> {
    const response = await api.patch<Session>(`/v1/api/sessions/${id}`, sessionData);
    return response.data;
  },

  // Delete a session
  async deleteSession(id: string): Promise<void> {
    await api.delete(`/v1/api/sessions/${id}`);
  },

  // Get answers for a session
  async getSessionAnswers(id: string): Promise<any[]> { // Assuming 'any[]' for now, will define Answer interface later
    const response = await api.get<any[]>(`/v1/api/sessions/${id}/answers`);
    return response.data;
  },

  // Complete an exam session and calculate score
  async completeSession(id: string): Promise<Session> {
    const response = await api.patch<Session>(`/v1/api/sessions/${id}/complete`);
    return response.data;
  },

  // Recalculate score for a completed session
  async recalculateScore(id: string): Promise<Session> {
    const response = await api.patch<Session>(`/v1/api/sessions/${id}/recalculate-score`);
    return response.data;
  },

  // Get session results with course and course group breakdown
  async getSessionResults(id: string): Promise<SessionResult> {
    const response = await api.get<SessionResult>(`/v1/api/sessions/${id}/results`);
    return response.data;
  }
};
