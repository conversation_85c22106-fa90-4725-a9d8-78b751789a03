import React, { useEffect, useState } from 'react';
import { StyleSheet, SafeAreaView, View } from 'react-native';
import { Surface, ActivityIndicator } from 'react-native-paper';
import { MajorCourse, majorCourseService } from '@/services/majorCourseService';
import { useLocalSearchParams, router } from 'expo-router';
import KeyboardAvoidingWrapper from '@/components/KeyboardAvoidingWrapper';
import {
  PaperFormAppBar,
  PaperTextInput,
  PaperButton,
  PaperFormField,
  PaperFormSection,
  PaperAlertDialog,
  PaperBody,
} from '@/components/paper';
import { t } from '@/constants/Localization';

export default function MajorCourseEditScreen() {
  const { majorId, courseId } = useLocalSearchParams();
  const [currentMajorId, setCurrentMajorId] = useState('');
  const [currentCourseId, setCurrentCourseId] = useState('');
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [errors, setErrors] = useState<{[key: string]: string}>({});
  const [showAlert, setShowAlert] = useState(false);
  const [alertTitle, setAlertTitle] = useState('');
  const [alertMessage, setAlertMessage] = useState('');

  useEffect(() => {
    if (majorId && courseId) {
      const fetchMajorCourse = async () => {
        try {
          const data = await majorCourseService.getMajorCourse(majorId as string, courseId as string);
          setCurrentMajorId(data.majorId);
          setCurrentCourseId(data.courseId);
        } catch (error) {
          setAlertTitle(t('error') || 'خطا');
          setAlertMessage('خطا در دریافت جزئیات ارتباط رشته-درس برای ویرایش.');
          setShowAlert(true);
          console.error('Failed to fetch major-course:', error);
        } finally {
          setLoading(false);
        }
      };
      fetchMajorCourse();
    } else {
      setAlertTitle(t('error') || 'خطا');
      setAlertMessage('شناسه رشته یا درس برای ویرایش ارائه نشده است.');
      setShowAlert(true);
      setLoading(false);
    }
  }, [majorId, courseId]);

  const validateForm = () => {
    const newErrors: {[key: string]: string} = {};

    if (!currentMajorId.trim()) {
      newErrors.currentMajorId = 'شناسه رشته الزامی است';
    }

    if (!currentCourseId.trim()) {
      newErrors.currentCourseId = 'شناسه درس الزامی است';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleUpdateMajorCourse = async () => {
    if (!validateForm()) {
      return;
    }

    if (!majorId || !courseId) {
      setAlertTitle(t('error') || 'خطا');
      setAlertMessage('شناسه اصلی رشته یا درس موجود نیست.');
      setShowAlert(true);
      return;
    }

    setSaving(true);
    try {
      await majorCourseService.updateMajorCourse(majorId as string, courseId as string, {
        majorId: currentMajorId,
        courseId: currentCourseId,
      });

      setAlertTitle(t('success') || 'موفقیت');
      setAlertMessage('ارتباط رشته-درس با موفقیت به‌روزرسانی شد!');
      setShowAlert(true);

      // Navigate back after showing success message
      setTimeout(() => {
        router.back();
      }, 1500);
    } catch (error) {
      setAlertTitle(t('error') || 'خطا');
      setAlertMessage('به‌روزرسانی ارتباط رشته-درس انجام نشد. لطفا دوباره امتحان کنید.');
      setShowAlert(true);
      console.error('Failed to update major-course relation:', error);
    } finally {
      setSaving(false);
    }
  };

  if (loading) {
    return (
      <SafeAreaView style={styles.container}>
        <PaperFormAppBar
          title="ویرایش ارتباط رشته-درس"
          showSave={false}
        />
        <Surface style={styles.content}>
          <View style={styles.loadingContainer}>
            <ActivityIndicator size="large" />
            <PaperBody style={styles.loadingText}>در حال بارگذاری ارتباط رشته-درس برای ویرایش...</PaperBody>
          </View>
        </Surface>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      {/* App Bar */}
      <PaperFormAppBar
        title={t('editMajorCourse') || 'ویرایش ارتباط رشته-درس'}
        onSavePress={handleUpdateMajorCourse}
        saveDisabled={saving}
      />

      {/* Content */}
      <Surface style={styles.content}>
        <KeyboardAvoidingWrapper>
          <PaperFormSection title={t('relationDetails') || 'جزئیات ارتباط'}>
            <PaperBody style={styles.originalInfo}>
              شناسه اصلی رشته: {majorId}
            </PaperBody>
            <PaperBody style={styles.originalInfo}>
              شناسه اصلی درس: {courseId}
            </PaperBody>

            <PaperFormField
              label={t('newMajorId') || 'شناسه جدید رشته'}
              error={errors.currentMajorId}
              required
            >
              <PaperTextInput
                value={currentMajorId}
                onChangeText={setCurrentMajorId}
                placeholder="شناسه جدید رشته را وارد کنید"
                disabled={saving}
                error={!!errors.currentMajorId}
              />
            </PaperFormField>

            <PaperFormField
              label={t('newCourseId') || 'شناسه جدید درس'}
              error={errors.currentCourseId}
              required
            >
              <PaperTextInput
                value={currentCourseId}
                onChangeText={setCurrentCourseId}
                placeholder="شناسه جدید درس را وارد کنید"
                disabled={saving}
                error={!!errors.currentCourseId}
              />
            </PaperFormField>
          </PaperFormSection>
        </KeyboardAvoidingWrapper>
      </Surface>

      {/* Alert Dialog */}
      <PaperAlertDialog
        visible={showAlert}
        onDismiss={() => setShowAlert(false)}
        title={alertTitle}
        message={alertMessage}
      />
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  content: {
    flex: 1,
    margin: 16,
    borderRadius: 8,
    elevation: 2,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 24,
  },
  loadingText: {
    marginTop: 16,
    textAlign: 'center',
  },
  originalInfo: {
    marginBottom: 8,
    color: '#666',
    fontStyle: 'italic',
  },
});
