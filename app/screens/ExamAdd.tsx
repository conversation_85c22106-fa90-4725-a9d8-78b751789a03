import React, { useState, useEffect } from 'react';
import { StyleSheet, SafeAreaView, ScrollView, View } from 'react-native';
import { Surface } from 'react-native-paper';
import { examService } from '@/services/examService';
import { majorService, Major } from '@/services/majorService';
import { questionService, Question } from '@/services/questionService';
import KeyboardAvoidingWrapper from '@/components/KeyboardAvoidingWrapper';
import { router } from 'expo-router';
import {
  PaperFormAppBar,
  PaperTextInput,
  PaperButton,
  PaperFormField,
  PaperFormSection,
  PaperAlertDialog,
  PaperBody,
  PaperCard,
  PaperCheckbox,
} from '@/components/paper';
import { PaperSelect } from 'react-native-paper-select'; // Correct import
import { t } from '@/constants/Localization';

export default function ExamAddScreen() {
  const [name, setName] = useState('');
  const [durationMinutes, setDurationMinutes] = useState('');
  const [instructions, setInstructions] = useState('');
  const [selectedMajor, setSelectedMajor] = useState<Major[]>([]);
  const [selectedMajorText, setSelectedMajorText] = useState<string>(''); // New state for displayed text
  const [selectedQuestions, setSelectedQuestions] = useState<string[]>([]);
  const [majors, setMajors] = useState<Major[]>([]);
  const [questions, setQuestions] = useState<Question[]>([]);
  const [loading, setLoading] = useState(false);
  const [errors, setErrors] = useState<{[key: string]: string}>({});
  const [showAlert, setShowAlert] = useState(false);
  const [alertTitle, setAlertTitle] = useState('');
  const [alertMessage, setAlertMessage] = useState('');

  useEffect(() => {
    const fetchMajorsAndQuestions = async () => {
      try {
        const majorsData = await majorService.getMajors();
        setMajors(majorsData);
        const questionsData = await questionService.getQuestions();
        setQuestions(questionsData);
      } catch (error) {
        setAlertTitle(t('error') || 'خطا');
        setAlertMessage('خطا در بارگیری اطلاعات');
        setShowAlert(true);
        console.error('Failed to fetch majors or questions:', error);
      }
    };
    fetchMajorsAndQuestions();
  }, []);

  const validateForm = () => {
    const newErrors: {[key: string]: string} = {};

    if (!name.trim()) {
      newErrors.name = 'نام آزمون الزامی است';
    }

    if (!durationMinutes.trim()) {
      newErrors.durationMinutes = 'مدت آزمون الزامی است';
    } else if (isNaN(Number(durationMinutes)) || Number(durationMinutes) <= 0) {
      newErrors.durationMinutes = 'مدت آزمون باید عدد مثبت باشد';
    }

    if (!instructions.trim()) {
      newErrors.instructions = 'دستورالعمل الزامی است';
    }

    if (selectedMajor.length === 0) {
      newErrors.majorId = 'انتخاب رشته الزامی است';
    }

    if (selectedQuestions.length === 0) {
      newErrors.questions = 'حداقل یک سوال باید انتخاب شود';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleAddExam = async () => {
    if (!validateForm()) {
      return;
    }

    setLoading(true);
    try {
      await examService.createExam({
        name,
        duration_minutes: Number(durationMinutes),
        instructions,
        majorId: Number(selectedMajor[0].id),
        questionIds: selectedQuestions,
      });

      setAlertTitle(t('success') || 'موفقیت');
      setAlertMessage('آزمون با موفقیت اضافه شد!');
      setShowAlert(true);

      // Navigate back after showing success message
      setTimeout(() => {
        router.back();
      }, 1500);
    } catch (error) {
      setAlertTitle(t('error') || 'خطا');
      setAlertMessage('افزودن آزمون انجام نشد. لطفا دوباره امتحان کنید.');
      setShowAlert(true);
      console.error('Failed to add exam:', error);
    } finally {
      setLoading(false);
    }
  };

  const toggleQuestionSelection = (questionId: string) => {
    setSelectedQuestions((prevSelected) =>
      prevSelected.includes(questionId)
        ? prevSelected.filter((id) => id !== questionId)
        : [...prevSelected, questionId]
    );
  };

  return (
    <SafeAreaView style={styles.container}>
      {/* App Bar */}
      <PaperFormAppBar
        title={t('addExam') || 'افزودن آزمون'}
        onSavePress={() => {}} // Handled by form submit button
        saveDisabled={loading}
        showSave={false} // Using form submit button instead
      />

      {/* Content */}
      <Surface style={styles.content}>
        <ScrollView>
          <KeyboardAvoidingWrapper>
            <PaperFormSection title="جزئیات آزمون">
              <PaperFormField
                label="نام آزمون"
                error={errors.name}
                required
              >
                <PaperTextInput
                  value={name}
                  onChangeText={setName}
                  placeholder="نام آزمون را وارد کنید"
                  disabled={loading}
                  error={!!errors.name}
                />
              </PaperFormField>

              <PaperFormField
                label="مدت آزمون (دقیقه)"
                error={errors.durationMinutes}
                required
              >
                <PaperTextInput
                  value={durationMinutes}
                  onChangeText={setDurationMinutes}
                  placeholder="مدت آزمون به دقیقه"
                  disabled={loading}
                  keyboardType="numeric"
                  error={!!errors.durationMinutes}
                />
              </PaperFormField>

              <PaperFormField
                label="دستورالعمل"
                error={errors.instructions}
                required
              >
                <PaperTextInput
                  value={instructions}
                  onChangeText={setInstructions}
                  placeholder="دستورالعمل آزمون را وارد کنید"
                  disabled={loading}
                  multiline
                  numberOfLines={3}
                  error={!!errors.instructions}
                />
              </PaperFormField>

              <PaperFormField
                label="رشته تحصیلی"
                error={errors.majorId}
                required
              >
                <PaperSelect
                  label="انتخاب رشته تحصیلی"
                  value={selectedMajorText} // Use the new state for displayed text
                  onSelection={(value: { text: string; selectedList: { _id: string; value: string }[]; }) => {
                    setSelectedMajorText(value.text);
                    // Map selectedList (ListItem[]) back to Major[]
                    const selectedMajorsData = value.selectedList.map(item => {
                      const foundMajor = majors.find(major => major.id === item._id);
                      return foundMajor || { id: item._id, name: item.value, description: '' }; // Fallback if not found
                    });
                    setSelectedMajor(selectedMajorsData);
                  }}
                  arrayList={majors.map(major => ({ _id: major.id, value: major.name }))}
                  selectedArrayList={selectedMajor.map(major => ({ _id: major.id, value: major.name }))} // Added required prop
                  multiEnable={false}
                  disabled={loading}
                  errorText={errors.majorId}
                />
              </PaperFormField>
            </PaperFormSection>

            <PaperFormSection title="انتخاب سوال‌ها">
              <PaperFormField
                label=""
                error={errors.questions}
              >
                <View style={styles.questionsContainer}>
                  {questions.map((question) => (
                    <PaperCard key={question.id} style={styles.questionCard}>
                      <View style={styles.questionRow}>
                        <PaperCheckbox
                          status={selectedQuestions.includes(question.id) ? 'checked' : 'unchecked'}
                          onPress={() => toggleQuestionSelection(question.id)}
                          disabled={loading}
                        />
                        <PaperBody style={styles.questionText}>
                          {question.question_text}
                        </PaperBody>
                      </View>
                    </PaperCard>
                  ))}
                </View>
              </PaperFormField>
            </PaperFormSection>

            <PaperButton
              title={loading ? 'در حال افزودن...' : 'افزودن آزمون'}
              onPress={handleAddExam}
              disabled={loading}
              loading={loading}
              mode="contained"
              style={styles.submitButton}
              icon="plus"
            />
          </KeyboardAvoidingWrapper>
        </ScrollView>
      </Surface>

      {/* Alert Dialog */}
      <PaperAlertDialog
        visible={showAlert}
        onDismiss={() => setShowAlert(false)}
        title={alertTitle}
        message={alertMessage}
      />
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  content: {
    flex: 1,
    margin: 16,
    borderRadius: 8,
    elevation: 2,
  },
  questionsContainer: {
    marginTop: 8,
  },
  questionCard: {
    marginBottom: 8,
    elevation: 1,
  },
  questionRow: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 12,
  },
  questionText: {
    flex: 1,
    marginLeft: 12,
  },
  submitButton: {
    marginTop: 24,
    marginHorizontal: 16,
    marginBottom: 16,
  },
});
