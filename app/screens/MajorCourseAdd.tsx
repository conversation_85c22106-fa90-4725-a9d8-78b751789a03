import React, { useState } from 'react';
import { StyleSheet, SafeAreaView } from 'react-native';
import { Surface } from 'react-native-paper';
import { majorCourseService } from '@/services/majorCourseService';
import { router } from 'expo-router';
import KeyboardAvoidingWrapper from '@/components/KeyboardAvoidingWrapper';
import {
  PaperFormAppBar,
  PaperTextInput,
  PaperButton,
  PaperFormField,
  PaperFormSection,
  PaperAlertDialog,
} from '@/components/paper';
import { t } from '@/constants/Localization';

export default function MajorCourseAddScreen() {
  const [majorId, setMajorId] = useState('');
  const [courseId, setCourseId] = useState('');
  const [loading, setLoading] = useState(false);
  const [errors, setErrors] = useState<{[key: string]: string}>({});
  const [showAlert, setShowAlert] = useState(false);
  const [alertTitle, setAlertTitle] = useState('');
  const [alertMessage, setAlertMessage] = useState('');

  const validateForm = () => {
    const newErrors: {[key: string]: string} = {};

    if (!majorId.trim()) {
      newErrors.majorId = 'شناسه رشته الزامی است';
    }

    if (!courseId.trim()) {
      newErrors.courseId = 'شناسه درس الزامی است';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleAddMajorCourse = async () => {
    if (!validateForm()) {
      return;
    }

    setLoading(true);
    try {
      await majorCourseService.createMajorCourse({ majorId, courseId });

      setAlertTitle(t('success') || 'موفقیت');
      setAlertMessage('ارتباط رشته-درس با موفقیت اضافه شد!');
      setShowAlert(true);

      // Navigate back after showing success message
      setTimeout(() => {
        router.back();
      }, 1500);
    } catch (error) {
      setAlertTitle(t('error') || 'خطا');
      setAlertMessage('افزودن ارتباط رشته-درس انجام نشد. لطفا دوباره امتحان کنید.');
      setShowAlert(true);
      console.error('Failed to add major-course relation:', error);
    } finally {
      setLoading(false);
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      {/* App Bar */}
      <PaperFormAppBar
        title={t('addMajorCourse') || 'افزودن ارتباط رشته-درس'}
        onSavePress={() => {}} // Handled by form submit button
        saveDisabled={loading}
        showSave={false} // Using form submit button instead
      />

      {/* Content */}
      <Surface style={styles.content}>
        <KeyboardAvoidingWrapper>
          <PaperFormSection title={t('relationDetails') || 'جزئیات ارتباط'}>
            <PaperFormField
              label={t('majorId') || 'شناسه رشته'}
              error={errors.majorId}
              required
            >
              <PaperTextInput
                value={majorId}
                onChangeText={setMajorId}
                placeholder="شناسه رشته را وارد کنید"
                disabled={loading}
                error={!!errors.majorId}
              />
            </PaperFormField>

            <PaperFormField
              label={t('courseId') || 'شناسه درس'}
              error={errors.courseId}
              required
            >
              <PaperTextInput
                value={courseId}
                onChangeText={setCourseId}
                placeholder="شناسه درس را وارد کنید"
                disabled={loading}
                error={!!errors.courseId}
              />
            </PaperFormField>

            <PaperButton
              title={loading ? 'در حال افزودن...' : t('addRelation') || 'افزودن ارتباط'}
              onPress={handleAddMajorCourse}
              disabled={loading}
              loading={loading}
              mode="contained"
              style={styles.submitButton}
              icon="plus"
            />
          </PaperFormSection>
        </KeyboardAvoidingWrapper>
      </Surface>

      {/* Alert Dialog */}
      <PaperAlertDialog
        visible={showAlert}
        onDismiss={() => setShowAlert(false)}
        title={alertTitle}
        message={alertMessage}
      />
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  content: {
    flex: 1,
    margin: 16,
    borderRadius: 8,
    elevation: 2,
  },
  submitButton: {
    marginTop: 24,
    marginHorizontal: 16,
    marginBottom: 16,
  },
});
